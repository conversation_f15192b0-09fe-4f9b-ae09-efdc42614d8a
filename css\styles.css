/* ========================================
   CSS Reset and Base Styles
   ======================================== */

/* Modern CSS Reset */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 62.5%; /* 1rem = 10px */
}

body {
    font-family: var(--font-family-primary);
    font-size: 1.6rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--white);
    overflow-x: hidden;
    padding-top: 12rem; /* Adjust based on header height */
}

/* ========================================
   CSS Custom Properties (Color Scheme)
   ======================================== */

:root {
    /* Primary Medical Colors - Based on IMA Logo */
    --primary-blue: #0B4C7A;        /* Deep Medical Blue */
    --primary-blue-light: #1565C0;  /* Light Medical Blue */
    --primary-blue-dark: #082F4A;   /* Dark Medical Blue */

    /* Secondary Medical Colors */
    --secondary-green: #2E7D32;     /* Medical Green */
    --secondary-green-light: #4CAF50; /* Light Medical Green */
    --secondary-green-dark: #1B5E20; /* Dark Medical Green */

    /* Accent Colors */
    --accent-red: #C62828;          /* Medical Red/Emergency */
    --accent-red-light: #EF5350;   /* Light Medical Red */
    --accent-orange: #E65100;       /* Medical Orange */
    --accent-gold: #FF8F00;         /* Medical Gold/Warning */

    /* Professional Grays */
    --white: #FFFFFF;
    --gray-50: #FAFAFA;
    --gray-100: #F5F5F5;
    --gray-200: #EEEEEE;
    --gray-300: #E0E0E0;
    --gray-400: #BDBDBD;
    --gray-500: #9E9E9E;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* Semantic Colors */
    --text-primary: var(--gray-800);
    --text-secondary: var(--gray-600);
    --text-light: var(--gray-500);
    --bg-primary: var(--white);
    --bg-secondary: var(--gray-50);
    --border-color: var(--gray-200);
      /* Medical Specific Colors */
    --success-color: var(--secondary-green);
    --warning-color: var(--accent-gold);
    --error-color: var(--accent-red);
    --info-color: var(--primary-blue-light);

    /* Medical Theme Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-green) 0%, var(--secondary-green-light) 100%);
    --gradient-accent: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--secondary-green-dark) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(11, 76, 122, 0.08);
    --shadow-md: 0 4px 6px -1px rgba(11, 76, 122, 0.15), 0 2px 4px -1px rgba(11, 76, 122, 0.10);
    --shadow-lg: 0 10px 15px -3px rgba(11, 76, 122, 0.15), 0 4px 6px -2px rgba(11, 76, 122, 0.08);
    --shadow-xl: 0 20px 25px -5px rgba(11, 76, 122, 0.18), 0 10px 10px -5px rgba(11, 76, 122, 0.10);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;    --radius-xl: 1rem;
    --radius-full: 50%;

    /* Typography */
    --font-family-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Container Sizes */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ========================================
   Utility Classes
   ======================================== */

.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 2rem;
}

@media (min-width: 1536px) {
    .container {
        max-width: var(--container-2xl);
    }
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.4rem;
    font-weight: 500;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: var(--white);
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-block {
    width: 100%;
}

/* Custom styles for Register Now button in nav-actions */
.nav-actions .btn-primary {
    padding: 1.2rem 2.5rem; /* Slightly larger padding */
    font-size: 1.5rem; /* Slightly larger font */
    box-shadow: var(--shadow-md);
}

/* ========================================
   Header Styles
   ======================================== */

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--white);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.header.scrolled {
    box-shadow: var(--shadow-md);
}

/* Top Bar */
.top-bar {
    background: var(--gradient-accent);
    color: var(--white);
    padding: 0.5rem 0;
    font-size: 1.1rem;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.contact-info {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    opacity: 0.8;
    font-size: 1rem;
}

.info-item i {
    font-size: 1rem;
}

/* Clickable info items (email and phone) */
.info-link {
    color: inherit;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.info-link:hover {
    opacity: 1;
    transform: translateY(-1px);
    color: var(--white);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    backdrop-filter: blur(10px);
    position: relative;
}

.social-link i {
    font-size: 1.1rem;
    line-height: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.social-link:hover {
    background: var(--white);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

/* Main Navigation */
.main-nav {
    padding: 1rem 0;
    background: #FFFFFF;
    position: relative;
    border-bottom: 1px solid var(--gray-200);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    position: relative;
    max-width: 100%;
    overflow: visible;
}

/* Logo Section */
.logo-section {
    flex-shrink: 0;
    min-width: max-content;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    text-decoration: none;
    color: var(--text-primary);
}

.logo-image {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.main-logo {
    height: 5rem;
    width: auto;
    max-width: 8rem;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.mobile-logo {
    height: 5rem;
    width: auto;
    max-width: 7rem;
    object-fit: contain;
}

/* Logo Circle - Deprecated, keeping for backward compatibility */
.logo-circle {
    width: 5rem;
    height: 5rem;
    background: var(--gradient-secondary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.logo-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.conference-title {
    font-family: var(--font-family-primary);
    font-size: 2.4rem;
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    line-height: 1.2;
    margin: 0;
}

.conference-year {
    font-size: 1.6rem;
    font-weight: 500;
    color: var(--secondary-green);
    margin-top: -0.2rem;
}

/* Navigation Menu */
.nav-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    overflow-x: visible; /* Changed from auto to visible */
    overflow-y: visible;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 0 1rem;
    margin: 0 1rem;
    position: static;
    z-index: 100;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 0.3rem;
    align-items: center;
    white-space: nowrap;
    min-width: max-content;
    margin: 0;
    padding: 0;
    position: relative;
    /* overflow: visible; */ /* Removed this line */
    z-index: 101;
}

.nav-item {
    position: relative;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.2rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    white-space: nowrap;
    min-width: max-content;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-blue-dark);
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(11, 76, 122, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(11, 76, 122, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.nav-link i {
    font-size: 1rem;
    transition: transform var(--transition-fast);
}

/* Dropdown Arrow Animation */
.nav-item.dropdown:hover .nav-link i {
    transform: rotate(180deg);
}

/* ===== IMPROVED DROPDOWN STYLES ===== */

/* Dropdown Menu Container */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 5px);
    left: 0;
    min-width: 220px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    list-style: none;
    padding: 8px 0;
    margin: 0;
    z-index: 9999;

    /* Initially hidden */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Show dropdown on hover */
.nav-item.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Keep dropdown visible when hovering over it */
.dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown Menu Items */
.dropdown-menu li {
    margin: 0;
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa !important;
    color: #0b4c7a !important;
    border-left-color: #2e7d32 !important;
    padding-left: 25px !important;
}

/* Action Buttons */
.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-shrink: 0;
    margin-left: 1rem;
    position: relative;
    z-index: 3;
}

/* Mobile Toggle */
.mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 3rem;
    height: 2.4rem;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
}

.hamburger {
    width: 100%;
    height: 3px;
    background: var(--primary-blue);
    border-radius: 2px;
    transition: all var(--transition-fast);
}

.mobile-toggle.active .hamburger:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-toggle.active .hamburger:nth-child(2) {
    opacity: 0;
}

.mobile-toggle.active .hamburger:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ========================================
   Mobile Menu Styles
   ======================================== */

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(5px);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 32rem;
    max-width: 90vw;
    height: 100vh;
    background: var(--white);
    padding: 2rem;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    overflow-y: auto;
    box-shadow: 0 0 30px rgba(11, 76, 122, 0.15);
}

.mobile-menu-overlay.active .mobile-menu {
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(11, 76, 122, 0.1);
}

.mobile-menu-header .logo-text h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.mobile-menu-header .mobile-logo {
    height: 4.5rem !important;
    width: auto !important;
    max-width: 6.5rem !important;
    object-fit: contain !important;
}

.mobile-menu-header .logo-container {
    gap: 1.5rem;
}

.mobile-close {
    width: 3rem;
    height: 3rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--primary-blue);
    font-size: 1.5rem;
    transition: all var(--transition-fast);
}

.mobile-close:hover {
    background: rgba(11, 76, 122, 0.1);
    transform: rotate(90deg);
}

.mobile-nav-list {
    list-style: none;
    margin-bottom: 3rem;
}

.mobile-nav-list > li {
    margin-bottom: 0.5rem;
}

.mobile-nav-link {
    display: block;
    padding: 1.5rem 1rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 1.6rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    border: none;
    position: relative;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
    background: rgba(11, 76, 122, 0.1);
    color: var(--primary-blue-dark);
    transform: translateX(0.3rem);
}

.mobile-submenu {
    list-style: none;
    margin-left: 2rem;
    margin-top: 0.5rem;
    display: none;
    transition: all var(--transition-fast);
}

.mobile-submenu a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.mobile-submenu a:hover,
.mobile-submenu a:focus,
.mobile-submenu a:active {
    background-color: #f8f9fa !important;
    color: #0b4c7a !important;
    border-left-color: #2e7d32 !important;
    padding-left: 25px !important;
}

/* Mobile dropdown styles */
.mobile-dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-arrow {
    font-size: 1rem;
    transition: transform var(--transition-fast);
}

.mobile-dropdown.active .mobile-arrow {
    transform: rotate(180deg);
}

.mobile-dropdown.active .mobile-submenu {
    display: block;
}

.mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Dropdown positioning */
.nav-item.dropdown {
    position: relative !important;
}

/* Right align for last dropdown items */
.nav-item.dropdown:last-child .dropdown-menu,
.nav-item.dropdown:nth-last-child(2) .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}

/* Remove active menu underline */
.nav-link.active::after {
    display: none !important;
}

/* Registration Button in Navigation */
.nav-actions .btn {
    font-size: 1.3rem;
    padding: 0.8rem 1.6rem;
    font-weight: 600;
    white-space: nowrap;
}

.nav-actions .btn i {
    font-size: 1.1rem;
}



/* ========================================
   Main Content Sections
   ======================================== */

/* Hero Section */
.hero {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 16rem 0 8rem;
    position: relative;
    overflow: hidden;
    margin-top: 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(11, 76, 122, 0.9) 0%, rgba(46, 125, 50, 0.8) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-text {
    text-align: center;
    max-width: 80rem;
    margin: 0 auto;
}

.hero-title {
    font-size: 4.8rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 2.4rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.hero-description {
    font-size: 1.8rem;
    line-height: 1.6;
    margin-bottom: 4rem;
    color: rgba(255, 255, 255, 0.85);
    max-width: 70rem;
    margin-left: auto;
    margin-right: auto;
}

.hero-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem 2rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-card i {
    font-size: 2.4rem;
    color: var(--accent-gold);
}

.info-card div {
    display: flex;
    flex-direction: column;
}

.info-card strong {
    font-size: 1.6rem;
    font-weight: var(--font-weight-semibold);
}

.info-card span {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: 1.5rem 3rem;
    font-size: 1.6rem;
    font-weight: var(--font-weight-semibold);
}

.hero-actions .btn-primary {
    background: var(--white);
    color: var(--primary-blue);
    border-color: var(--white);
}

.hero-actions .btn-primary:hover {
    background: var(--gray-100);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.hero-actions .btn-outline {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.hero-actions .btn-outline:hover {
    background: var(--white);
    color: var(--primary-blue);
}

/* Quick Info Section */
.quick-info {
    padding: 6rem 0;
    background: var(--gray-50);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 3rem;
    margin-top: 2rem;
}

.info-item {
    text-align: center;
    padding: 1.2rem 1.1rem;
    cursor: pointer;
    font-size: 13px;
    /* background: var(--white); */
    /* border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal); */
    /* border-top: 4px solid var(--primary-blue); */
}

/* .info-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-top-color: var(--secondary-green);
} */

.info-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 6rem;
    height: 6rem;
    background: var(--gradient-secondary);
    color: var(--white);
    border-radius: var(--radius-full);
    margin-bottom: 2rem;
    font-size: 2.4rem;
}

.info-item h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.info-item p {
    font-size: 1.4rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* About Section */
.about-section {
    padding: 8rem 0;
    background: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: 6rem;
}

.section-header h2 {
    font-size: 3.6rem;
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
}

.section-header p {
    font-size: 1.8rem;
    color: var(--text-secondary);
    max-width: 60rem;
    margin: 0 auto;
}

.about-content {
    max-width: 90rem;
    margin: 0 auto;
}

.about-text {
    font-size: 1.6rem;
    line-height: 1.8;
    color: var(--text-primary);
    margin-bottom: 4rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--secondary-green);
}

.feature i {
    color: var(--secondary-green);
    font-size: 1.6rem;
}

.feature span {
    font-size: 1.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

/* ========================================
   Responsive Design
   ======================================== */

@media (max-width: 1200px) {
    .nav-link {
        padding: 1rem 1rem;
        font-size: 1.2rem;
    }

    .nav-list {
        gap: 0.2rem;
    }

    .container {
        padding: 0 1.5rem;
    }
}

@media (max-width: 1150px) {
    .nav-link {
        padding: 0.9rem 0.8rem;
        font-size: 1.15rem;
    }

    .nav-list {
        gap: 0.15rem;
    }

    .nav-actions .btn {
        padding: 0.7rem 1.4rem;
        font-size: 1.05rem;
    }
}

@media (max-width: 1024px) {
    .nav-actions .btn {
        padding: 0.6rem 1rem;
        font-size: 1rem;
    }

    .nav-link {
        padding: 0.8rem 0.6rem;
        font-size: 1.05rem;
    }

    .nav-list {
        gap: 0.05rem;
    }

    .nav-menu {
        margin: 0 0.4rem;
    }

    .nav-actions {
        margin-left: 0.4rem;
    }
}

@media (max-width: 1100px) {
    .nav-link {
        padding: 0.8rem 0.7rem;
        font-size: 1.1rem;
    }

    .nav-list {
        gap: 0.1rem;
    }

    .nav-actions .btn {
        padding: 0.6rem 1.1rem;
        font-size: 1rem;
    }
}

@media (max-width: 850px) {
    .nav-menu,
    .nav-actions {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }
}

@media (min-width: 851px) and (max-width: 950px) {
    .nav-link {
        padding: 0.7rem 0.5rem;
        font-size: 1rem;
    }

    .nav-list {
        gap: 0.02rem;
    }

    .nav-actions .btn {
        padding: 0.6rem 0.9rem;
        font-size: 0.95rem;
    }

    .nav-menu {
        margin: 0 0.2rem;
        padding: 0 0.3rem;
    }

    .nav-actions {
        margin-left: 0.3rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    .top-bar {
        padding: 0.4rem 0;
        font-size: 1rem;
    }

    .top-bar-content {
        justify-content: center;
        gap: 0;
    }

    .contact-info {
        justify-content: center;
        gap: 1rem;
    }

    .info-item {
        font-size: 1.3rem;
    }

    /* Hide calendar date and social links on mobile */
    .info-item:nth-child(3),
    .social-links {
        display: none;
    }

    .main-nav {
        padding: 1rem 0;
    }

    .nav-menu,
    .nav-actions {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }

    .main-logo {
        height: 4rem;
        max-width: 6rem;
    }

    .mobile-logo {
        height: 2.5rem;
        max-width: 4rem;
    }

    .conference-title {
        font-size: 2rem;
    }

    .conference-year {
        font-size: 1.4rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .top-bar {
        padding: 0.3rem 0;
    }

    .contact-info {
        gap: 0.8rem;
    }

    .info-item {
        font-size: 1.2rem;
    }

    .logo-container {
        gap: 1rem;
    }

    .main-logo {
        height: 3.5rem;
        max-width: 5rem;
    }

    .mobile-logo {
        height: 2rem;
        max-width: 3rem;
    }

    .conference-title {
        font-size: 1.6rem;
    }

    .conference-year {
        font-size: 1.1rem;
    }

    .mobile-menu {
        width: 100%;
        max-width: 100vw;
        padding: 1.5rem;
    }

    .mobile-nav-link {
        padding: 1.2rem 0.8rem;
        font-size: 1.5rem;
    }

    .top-bar-content {
        padding: 0.5rem 0;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 0 0.8rem;
    }

    .conference-title {
        font-size: 1.4rem;
    }

    .conference-year {
        font-size: 1rem;
    }

    .main-logo {
        height: 3rem;
        max-width: 4rem;
    }

    .mobile-logo {
        height: 1.8rem;
        max-width: 2.5rem;
    }

    .mobile-nav-link {
        padding: 1rem 0.5rem;
        font-size: 1.4rem;
    }

    .info-item {
        font-size: 0.8rem;
    }

    .contact-info {
        gap: 0.6rem;
    }
}

/* ========================================
   Large Screen Optimizations
   ======================================== */

@media (min-width: 1400px) {
    .nav-link {
        padding: 1.2rem 1.8rem;
        font-size: 1.4rem;
    }

    .nav-list {
        gap: 0.5rem;
    }

    .container {
        padding: 0 3rem;
    }
}

@media (min-width: 1600px) {
    .container {
        padding: 0 4rem;
    }

    .nav-link {
        padding: 1.2rem 2rem;
        font-size: 1.5rem;
    }

    .nav-list {
        gap: 0.6rem;
    }
}

/* ========================================
   Navigation Scroll Handling
   ======================================== */

.nav-content {
    position: relative;
}

/* Removed fade indicators to prevent white fading issues */

/* ========================================
   Performance Optimizations
   ======================================== */

/* Improve rendering performance */
.header,
.main-nav,
.dropdown-menu,
.mobile-menu-overlay {
    will-change: transform;
}

/* Reduce layout shift */
.logo-circle {
    contain: layout style paint;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ========================================
   Accessibility Improvements
   ======================================== */

/* Focus styles */
.btn:focus,
.nav-link:focus,
.social-link:focus,
.mobile-toggle:focus {
    outline: none;
    box-shadow: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    }
}

/* Dark mode support (basic) - Disabled for clean white theme */
@media (prefers-color-scheme: dark) {
    /* Keep everything white and clean - no dark mode */
    .header {
        background: var(--white);
        border-bottom: 1px solid var(--gray-200);
    }

    .main-nav {
        background: var(--white);
    }

    .dropdown-menu {
        background: var(--white);
        border-color: var(--gray-200);
    }

    .mobile-menu {
        background: var(--white);
    }

    .mobile-menu-overlay {
        background: rgba(255, 255, 255, 0.95);
    }
}

/* ========================================
   Page Content Spacing
   ======================================== */

/* Add top margin to compensate for fixed header */
body {
    padding-top: 12rem; /* Adjust based on header height */
}

/* Remove top padding on mobile for better spacing */
@media (max-width: 768px) {
    body {
        padding-top: 10rem;
    }
}

@media (max-width: 480px) {
    body {
        padding-top: 9rem;
    }
}
