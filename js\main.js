/**
 * IMA NATCON 2025 - Main JavaScript File
 * Handles navigation, mobile menu, and interactive features
 */

(function() {
    'use strict';

    // ========================================
    // Constants and Configuration
    // ========================================

    const CONFIG = {
        SCROLL_THRESHOLD: 50,
        DEBOUNCE_DELAY: 16,
        MOBILE_BREAKPOINT: 768,
        ANIMATION_DURATION: 300
    };

    // ========================================
    // DOM Elements
    // ========================================

    const elements = {
        header: null,
        mainNav: null,
        mobileToggle: null,
        mobileMenuOverlay: null,
        mobileMenu: null,
        mobileClose: null,
        navLinks: null,
        dropdowns: null
    };

    // ========================================
    // State Management
    // ========================================

    const state = {
        isScrolled: false,
        isMobileMenuOpen: false,
        activeDropdown: null,
        scrollPosition: 0
    };

    // ========================================
    // Utility Functions
    // ========================================

    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function for scroll events
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Check if device is mobile
     */
    function isMobile() {
        return window.innerWidth <= CONFIG.MOBILE_BREAKPOINT;
    }

    /**
     * Get scroll position
     */
    function getScrollPosition() {
        return window.pageYOffset || document.documentElement.scrollTop;
    }

    /**
     * Add/remove class with animation support
     */
    function toggleClass(element, className, add) {
        if (!element) return;

        if (add) {
            element.classList.add(className);
        } else {
            element.classList.remove(className);
        }
    }

    // ========================================
    // Header Scroll Effects
    // ========================================

    /**
     * Handle header scroll effects
     */
    function handleHeaderScroll() {
        const currentScrollPosition = getScrollPosition();
        const shouldBeScrolled = currentScrollPosition > CONFIG.SCROLL_THRESHOLD;

        if (shouldBeScrolled !== state.isScrolled) {
            state.isScrolled = shouldBeScrolled;
            toggleClass(elements.header, 'scrolled', shouldBeScrolled);
        }

        state.scrollPosition = currentScrollPosition;
    }

    // ========================================
    // Mobile Menu Functions
    // ========================================

    /**
     * Open mobile menu
     */
    function openMobileMenu() {
        if (state.isMobileMenuOpen) return;

        state.isMobileMenuOpen = true;

        // Add classes
        toggleClass(elements.mobileMenuOverlay, 'active', true);
        toggleClass(elements.mobileToggle, 'active', true);
        toggleClass(document.body, 'menu-open', true);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus trap
        if (elements.mobileClose) {
            setTimeout(() => elements.mobileClose.focus(), CONFIG.ANIMATION_DURATION);
        }

        // Accessibility
        elements.mobileToggle.setAttribute('aria-expanded', 'true');
    }

    /**
     * Close mobile menu
     */
    function closeMobileMenu() {
        if (!state.isMobileMenuOpen) return;

        state.isMobileMenuOpen = false;

        // Remove classes
        toggleClass(elements.mobileMenuOverlay, 'active', false);
        toggleClass(elements.mobileToggle, 'active', false);
        toggleClass(document.body, 'menu-open', false);

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to toggle button
        if (elements.mobileToggle) {
            setTimeout(() => elements.mobileToggle.focus(), CONFIG.ANIMATION_DURATION);
        }

        // Accessibility
        elements.mobileToggle.setAttribute('aria-expanded', 'false');
    }

    /**
     * Toggle mobile menu
     */
    function toggleMobileMenu() {
        if (state.isMobileMenuOpen) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    }

    /**
     * Toggle mobile dropdown submenu
     */
    function toggleMobileDropdown(event) {
        event.preventDefault();

        const dropdownToggle = event.target.closest('.mobile-dropdown-toggle');
        if (!dropdownToggle) return;

        const dropdown = dropdownToggle.closest('.mobile-dropdown');
        if (!dropdown) return;

        // Close other open dropdowns
        document.querySelectorAll('.mobile-dropdown.active').forEach(item => {
            if (item !== dropdown) {
                item.classList.remove('active');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('active');
    }

    // ========================================
    // Navigation Functions
    // ========================================

    /**
     * Handle navigation link clicks
     */
    function handleNavLinkClick(event) {
        const link = event.target.closest('.nav-link, .mobile-nav-link');
        if (!link) return;

        // Don't close menu if clicking on dropdown toggle
        if (link.classList.contains('mobile-dropdown-toggle')) {
            return;
        }

        const href = link.getAttribute('href');

        // Only close mobile menu for actual navigation links (not dropdown toggles)
        // and only for submenu items or main nav items without dropdowns
        const isSubmenuLink = link.closest('.mobile-submenu');
        const isMainNavWithoutDropdown = link.classList.contains('mobile-nav-link') &&
                                        !link.classList.contains('mobile-dropdown-toggle');

        if (state.isMobileMenuOpen && (isSubmenuLink || isMainNavWithoutDropdown)) {
            closeMobileMenu();
        }

        // Handle internal links (anchors)
        if (href && href.startsWith('#')) {
            event.preventDefault();

            const targetId = href.substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // Smooth scroll to target
                const headerHeight = elements.header ? elements.header.offsetHeight : 0;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Update active nav link
                updateActiveNavLink(link);
            }
        }
    }

    /**
     * Update active navigation link
     */
    function updateActiveNavLink(activeLink) {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link.active').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to clicked link
        if (activeLink && activeLink.classList.contains('nav-link')) {
            activeLink.classList.add('active');
        }    }

    // ========================================
    // Event Listeners
    // ========================================

    /**
     * Initialize all event listeners
     */
    function initEventListeners() {
        // Scroll events
        window.addEventListener('scroll', throttle(handleHeaderScroll, CONFIG.DEBOUNCE_DELAY));

        // Resize events
        window.addEventListener('resize', debounce(() => {
            // Close mobile menu on resize to desktop
            if (!isMobile() && state.isMobileMenuOpen) {
                closeMobileMenu();
            }
        }, 250));

        // Mobile toggle click
        if (elements.mobileToggle) {
            elements.mobileToggle.addEventListener('click', toggleMobileMenu);
        }

        // Mobile close button
        if (elements.mobileClose) {
            elements.mobileClose.addEventListener('click', closeMobileMenu);
        }

        // Mobile menu overlay click
        if (elements.mobileMenuOverlay) {
            elements.mobileMenuOverlay.addEventListener('click', (event) => {
                if (event.target === elements.mobileMenuOverlay) {
                    closeMobileMenu();
                }
            });
        }

        // Navigation clicks
        document.addEventListener('click', handleNavLinkClick);

        // Mobile dropdown toggles
        document.addEventListener('click', (event) => {
            if (event.target.closest('.mobile-dropdown-toggle')) {
                toggleMobileDropdown(event);
            }
        });

        // Mobile submenu links
        document.addEventListener('click', (event) => {
            const submenuLink = event.target.closest('.mobile-submenu a');
            if (submenuLink && state.isMobileMenuOpen) {
                // Close mobile menu when clicking submenu items
                setTimeout(() => {
                    closeMobileMenu();
                }, 100);
            }
        });

        // Keyboard events
        document.addEventListener('keydown', (event) => {
            // Escape key closes mobile menu
            if (event.key === 'Escape' && state.isMobileMenuOpen) {
                closeMobileMenu();
            }

            // Enter/Space on mobile toggle
            if ((event.key === 'Enter' || event.key === ' ') &&
                event.target === elements.mobileToggle) {
                event.preventDefault();
                toggleMobileMenu();
            }        });
    }

    // ========================================
    // Intersection Observer for Active Links
    // ========================================

    /**
     * Initialize intersection observer for updating active nav links
     */
    function initIntersectionObserver() {
        const sections = document.querySelectorAll('section[id]');

        if (sections.length === 0) return;

        const observerOptions = {
            rootMargin: '-20% 0px -70% 0px',
            threshold: 0
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.getAttribute('id');
                    const correspondingNavLink = document.querySelector(`.nav-link[href="#${id}"]`);

                    if (correspondingNavLink) {
                        updateActiveNavLink(correspondingNavLink);
                    }
                }
            });
        }, observerOptions);

        sections.forEach(section => observer.observe(section));
    }

    // ========================================
    // Touch Handling for Mobile
    // ========================================

    /**
     * Initialize touch handling for better mobile experience
     */
    function initTouchHandling() {
        // Add touch-specific event handlers
        let touchStartY = 0;
        let touchEndY = 0;

        // Handle swipe gestures on mobile menu
        if (elements.mobileMenu) {
            elements.mobileMenu.addEventListener('touchstart', (e) => {
                touchStartY = e.changedTouches[0].screenY;
            });

            elements.mobileMenu.addEventListener('touchend', (e) => {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipeGesture();
            });
        }

        function handleSwipeGesture() {
            // Close menu on swipe down (if at top of menu)
            if (touchEndY > touchStartY + 100 && elements.mobileMenu.scrollTop === 0) {
                closeMobileMenu();
            }
        }
    }

    // ========================================
    // Performance Optimizations
    // ========================================

    /**
     * Preload critical resources
     */
    function preloadResources() {
        const criticalImages = [
            // Add any critical images here
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    /**
     * Initialize service worker for caching (if needed)
     */
    function initServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    // ========================================
    // Accessibility Enhancements
    // ========================================

    /**
     * Initialize accessibility features
     */
    function initAccessibility() {
        // Add ARIA attributes
        if (elements.mobileToggle) {
            elements.mobileToggle.setAttribute('aria-expanded', 'false');
            elements.mobileToggle.setAttribute('aria-controls', 'mobileMenuOverlay');
            elements.mobileToggle.setAttribute('aria-label', 'Toggle navigation menu');
        }

        // Add skip link functionality
        const skipLink = document.querySelector('.skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', (event) => {
                event.preventDefault();
                const target = document.querySelector(skipLink.getAttribute('href'));
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        }

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--transition-fast', '0.01ms');
            document.documentElement.style.setProperty('--transition-normal', '0.01ms');
            document.documentElement.style.setProperty('--transition-slow', '0.01ms');
        }
    }

    // ========================================
    // DOM Content Loaded
    // ========================================

    /**
     * Initialize everything when DOM is ready
     */
    function initializeApp() {
        // Cache DOM elements
        elements.header = document.getElementById('header');
        elements.mainNav = document.getElementById('mainNav');
        elements.mobileToggle = document.getElementById('mobileToggle');
        elements.mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        elements.mobileMenu = document.querySelector('.mobile-menu');
        elements.mobileClose = document.getElementById('mobileClose');
        elements.navLinks = document.querySelectorAll('.nav-link');
        elements.dropdowns = document.querySelectorAll('.dropdown');
          // Initialize components
        initEventListeners();
        initAccessibility();
        initTouchHandling();
        preloadResources();

        // Initialize intersection observer after a short delay
        setTimeout(initIntersectionObserver, 100);

        // Initial scroll check
        handleHeaderScroll();

        console.log('IMA NATCON 2025 website initialized successfully');
    }

    // ========================================
    // Error Handling
    // ========================================

    /**
     * Global error handler
     */
    window.addEventListener('error', (event) => {
        console.error('JavaScript Error:', event.error);
        // You could send this to an error reporting service
    });

    /**
     * Unhandled promise rejection handler
     */
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled Promise Rejection:', event.reason);
        // You could send this to an error reporting service
    });

    // ========================================
    // Initialization
    // ========================================

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }

    // ========================================
    // Public API (if needed)
    // ========================================

    // Expose some functions globally if needed by other scripts
    window.IMANatcon = {
        openMobileMenu,
        closeMobileMenu,
        updateActiveNavLink
    };

})();
